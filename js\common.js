// 获取当前用户信息
function getCurrentUser() {
    let user = null;
    try {
        user = JSON.parse(localStorage.getItem('wechat_user'));
    } catch(e) {}

    if (user) {
        return {
            id: user.id,
            name: user.username,
            username: user.username,
            avatar: user.avatar || 'https://picsum.photos/seed/user/100/100',
            wechatId: user.wechatId || user.wechat_id || '未设置',
            cover: {
                type: 'image',
                source: 'https://picsum.photos/600/400'
            }
        };
    }

    // 默认用户信息（未登录时）
    return {
        name: '我的名字',
        avatar: 'https://picsum.photos/id/3/100/100',
        wechatId: 'myWechatId2024',
        cover: {
            type: 'image',
            source: 'https://picsum.photos/600/400'
        }
    };
}


// 全局禁用非错误级别的控制台输出（保留console.error）
(function() {
    // 仅在浏览器环境下生效
    if (typeof window !== 'undefined' && typeof console !== 'undefined') {
        const noop = function(){};
        // 去除调试日志
        console.log = noop;      // 普通日志
        console.info = noop;     // 信息日志
        console.debug = noop;    // 调试日志
        console.trace = noop;    // 调用栈
        console.warn = noop;     // 警告日志
    }
})();

// 当前用户信息（动态获取）
const currentUser = getCurrentUser();

// 更新用户信息
function updateCurrentUser() {
    const newUserData = getCurrentUser();
    Object.assign(currentUser, newUserData);

    // 通知其他页面更新用户信息
    if (typeof renderUserInfo === 'function') {
        renderUserInfo();
    }
}

// 屏蔽外部脚本错误和通知权限警告
(function() {
    // 保存原始的console.error方法
    const originalConsoleError = console.error;

    // 重写console.error来过滤外部错误
    console.error = function(...args) {
        const message = args.join(' ');

        // 屏蔽通知权限相关的警告
        if (message.includes('Notifications permission') ||
            message.includes('permission prompt') ||
            message.includes('permanently-removed.invalid')) {
            return;
        }

        // 屏蔽stadium.js相关错误
        if (message.includes('stadium.js') ||
            message.includes('stagewise.js')) {
            return;
        }

        // 其他错误正常显示
        originalConsoleError.apply(console, args);
    };

    // 屏蔽JavaScript运行时错误
    window.addEventListener('error', function(e) {
        // 屏蔽来自扩展或外部脚本的错误
        if (e.filename && (
            e.filename.includes('stadium.js') ||
            e.filename.includes('stagewise.js') ||
            e.filename.includes('extension') ||
            e.filename.includes('chrome-extension') ||
            e.filename.includes('moz-extension')
        )) {
            e.preventDefault();
            return false;
        }
    });

    // 屏蔽未捕获的Promise错误
    window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.stack && (
            e.reason.stack.includes('stadium.js') ||
            e.reason.stack.includes('stagewise.js') ||
            e.reason.stack.includes('extension')
        )) {
            e.preventDefault();
            return false;
        }
    });
})();

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 加载各个页面的HTML内容
    loadPageContent();

    // 绑定底部导航栏切换事件
    initTabBar();

    // 强制所有已登录用户刷新后都进入聊天页面
    setTimeout(() => {
        let user = null;
        try { user = JSON.parse(localStorage.getItem('wechat_user')); } catch(e) {}
        if (user) {
            // 清理所有URL参数
            if (window.location.search) {
                window.history.replaceState({}, document.title, window.location.pathname);
            }
            console.log('用户已登录，强制显示聊天页面');
            showPage('chat');
        } else {
            console.log('用户未登录，等待登录');
        }
    }, 100);

    // 图片查看器相关
    const imageViewerModal = document.getElementById('image-viewer-modal');
    const closeImageViewer = document.getElementById('close-image-viewer');
    const imageContainer = document.getElementById('image-container');
    const prevImage = document.getElementById('prev-image');
    const nextImage = document.getElementById('next-image');
    const imageCounter = document.getElementById('image-counter');

    // 视频查看器相关
    const videoViewerModal = document.getElementById('video-viewer-modal');
    const closeVideoViewer = document.getElementById('close-video-viewer');
    const videoPlayer = document.getElementById('video-player');

    // 图片查看器事件
    if (closeImageViewer) {
        closeImageViewer.addEventListener('click', closeImageViewerHandler);
    }
    if (prevImage) {
        prevImage.addEventListener('click', showPrevImage);
    }
    if (nextImage) {
        nextImage.addEventListener('click', showNextImage);
    }

    // 视频查看器事件
    if (closeVideoViewer) {
        closeVideoViewer.addEventListener('click', closeVideoViewerHandler);
    }

    // 键盘事件处理
    document.addEventListener('keydown', (e) => {
        // 图片查看器的键盘事件
        if (imageViewerModal && imageViewerModal.style.display === 'flex') {
            if (e.key === 'Escape') {
                closeImageViewerHandler();
            } else if (e.key === 'ArrowLeft') {
                showPrevImage();
            } else if (e.key === 'ArrowRight') {
                showNextImage();
            }
        }

        // 视频查看器的键盘事件
        if (videoViewerModal && videoViewerModal.style.display === 'flex' && e.key === 'Escape') {
            closeVideoViewerHandler();
        }
    });

    // 页面加载时检查登录状态
    let user = null;
    try { user = JSON.parse(localStorage.getItem('wechat_user')); } catch(e) {}
    if (!user) {
        showLoginModal();
    } else {
        // 已登录用户的默认页面处理由URL参数逻辑统一管理
        // 不再显示右上角用户信息栏，因为"我"页面已有用户信息
    }
});

// 加载各个页面的HTML内容
function loadPageContent() {
    // 获取所有页面容器
    const pages = {
        chat: document.getElementById('chat-page'),
        contacts: document.getElementById('contacts-page'),
        moments: document.getElementById('moments-page'),
        profile: document.getElementById('profile-page'),
        albums: document.getElementById('albums-page'),
        'album-detail': document.getElementById('album-detail-page')
    };

    // 加载各个页面的内容
    for (const [key, page] of Object.entries(pages)) {
        if (page) {
            // Add cache-busting parameter
            const cacheBuster = Date.now();
            fetch(`partials/${key}.html?v=${cacheBuster}`)
                .then(response => response.text())
                .then(html => {
                    page.innerHTML = html;
                    // 页面内容加载完成后，只初始化非相册页面的功能
                    if (key !== 'albums' && key !== 'album-detail') {
                        initPageFunctions(key);
                    }
                })
                .catch(error => {
                    console.error(`Failed to load ${key} page:`, error);
                });
        }
    }
}

// 初始化页面功能
function initPageFunctions(pageKey) {
    switch (pageKey) {
        case 'chat':
            if (typeof initChat === 'function') {
                initChat();
            }
            break;
        case 'contacts':
            if (typeof initContacts === 'function') {
                initContacts();
            } else {
                // initContacts函数未定义
            }
            break;
        case 'moments':
            if (typeof initMoments === 'function') {
                initMoments();
            }
            break;
        case 'profile':
            if (typeof initProfile === 'function') {
                initProfile();
            }
            break;
        case 'albums':
            if (typeof initAlbums === 'function') {
                initAlbums();
            }
            break;
        case 'album-detail':
            if (typeof initAlbumDetail === 'function') {
                initAlbumDetail();
            }
            break;
    }
}

// 显示指定页面
function showPage(pageKey) {
    const pages = document.querySelectorAll('.page');
    const targetPageId = pageKey + '-page';

    // 隐藏所有页面
    pages.forEach(page => {
        page.classList.remove('active');
    });

    // 显示目标页面
    const targetPage = document.getElementById(targetPageId);
    if (targetPage) {
        targetPage.classList.add('active');

        // 初始化页面功能（相册页面需要明确调用）
        if (pageKey === 'albums' || pageKey === 'album-detail') {
            // 只有在明确显示相册页面时才初始化
            initPageFunctions(pageKey);
        } else {
            initPageFunctions(pageKey);
        }

        // 更新底部导航栏状态
        const tabItems = document.querySelectorAll('.tab-item');
        tabItems.forEach(tab => {
            tab.classList.remove('active');
        });

        // 根据页面类型设置对应的导航栏状态
        let activeTabPage = '';
        if (['chat', 'contacts', 'moments', 'profile'].includes(pageKey)) {
            activeTabPage = targetPageId;
        } else if (['albums', 'album-detail'].includes(pageKey)) {
            // 相册相关页面激活"我"标签
            activeTabPage = 'profile-page';
        }

        if (activeTabPage) {
            tabItems.forEach(tab => {
                if (tab.getAttribute('data-page') === activeTabPage) {
                    tab.classList.add('active');
                }
            });
        }
    } else {
        console.error(`Page ${targetPageId} not found`);
    }
}

// 初始化底部导航栏
function initTabBar() {
    const tabItems = document.querySelectorAll('.tab-item');
    const pages = document.querySelectorAll('.page');

    tabItems.forEach(item => {
        item.addEventListener('click', () => {
            const pageId = item.getAttribute('data-page');

            // 隐藏所有页面，显示目标页面
            pages.forEach(page => {
                page.classList.remove('active');
            });
            document.getElementById(pageId).classList.add('active');

            // 重新初始化相应页面
            if (pageId === 'contacts-page') {
                if (typeof initContacts === 'function') {
                    // 检查是否已登录，如果是新登录用户，强制重新初始化
                    let user = null;
                    try { user = JSON.parse(localStorage.getItem('wechat_user')); } catch(e) {}
                    if (user && window.contactsInitialized === false) {
                        window.contactsInitialized = false; // 强制重新初始化
                    }
                    initContacts();
                }
            } else if (pageId === 'moments-page') {
                if (typeof initMoments === 'function') {
                    initMoments();
                }
            } else if (pageId === 'chat-page') {
                if (typeof initChat === 'function') {
                    initChat();
                }
            } else if (pageId === 'profile-page') {
                if (typeof initProfile === 'function') {
                    initProfile();
                }
            }

            // 更新底部导航栏的激活状态
            tabItems.forEach(tab => {
                tab.classList.remove('active');
            });
            item.classList.add('active');
        });
    });
}

// 表情相关
function insertEmoji(textArea, emoji) {
    if (!textArea) return;

    // 获取当前光标位置
    const startPos = textArea.selectionStart;
    const endPos = textArea.selectionEnd;

    // 在光标位置插入表情
    const textBefore = textArea.value.substring(0, startPos);
    const textAfter = textArea.value.substring(endPos);

    textArea.value = textBefore + emoji + textAfter;

    // 重新设置光标位置
    const newPos = startPos + emoji.length;
    textArea.setSelectionRange(newPos, newPos);

    // 聚焦文本框
    textArea.focus();
}

// 媒体查看器相关
let currentImageViewerState = {
    momentId: null,
    images: [],
    currentIndex: 0
};

// 图片查看器相关函数
function closeImageViewerHandler() {
    const imageViewerModal = document.getElementById('image-viewer-modal');
    const imageContainer = document.getElementById('image-container');

    if (!imageViewerModal) return;

    // 移除active类来触发淡出动画
    imageViewerModal.classList.remove('active');

    // 重置图片容器样式
    if (imageContainer) {
        imageContainer.style.opacity = '0';
        imageContainer.style.transform = 'scale(0.95)';
    }

    // 等待动画完成后隐藏弹窗
    setTimeout(() => {
        imageViewerModal.style.display = 'none';
        // 清空图片容器
        if (imageContainer) {
            imageContainer.innerHTML = '';
        }
    }, 300);

    // 重置状态
    currentImageViewerState = {
        momentId: null,
        images: [],
        currentIndex: 0
    };
}

function showPrevImage() {
    if (currentImageViewerState.images.length <= 1) return;

    currentImageViewerState.currentIndex--;

    // 循环到最后一张图片
    if (currentImageViewerState.currentIndex < 0) {
        currentImageViewerState.currentIndex = currentImageViewerState.images.length - 1;
    }

    // 更新图片
    const imageContainer = document.getElementById('image-container');
    if (!imageContainer) return;

    // 添加淡出效果
    imageContainer.style.opacity = '0';
    imageContainer.style.transform = 'scale(0.95)';

    setTimeout(() => {
        const img = imageContainer.querySelector('img');
        if (img) {
            img.src = currentImageViewerState.images[currentImageViewerState.currentIndex];

            // 图片加载完成后淡入
            img.onload = function() {
                imageContainer.style.opacity = '1';
                imageContainer.style.transform = 'scale(1)';
            };

            // 更新计数器
            updateImageCounter();
        }
    }, 150);
}

function showNextImage() {
    if (currentImageViewerState.images.length <= 1) return;

    currentImageViewerState.currentIndex++;

    // 循环到第一张图片
    if (currentImageViewerState.currentIndex >= currentImageViewerState.images.length) {
        currentImageViewerState.currentIndex = 0;
    }

    // 更新图片
    const imageContainer = document.getElementById('image-container');
    if (!imageContainer) return;

    // 添加淡出效果
    imageContainer.style.opacity = '0';
    imageContainer.style.transform = 'scale(0.95)';

    setTimeout(() => {
        const img = imageContainer.querySelector('img');
        if (img) {
            img.src = currentImageViewerState.images[currentImageViewerState.currentIndex];

            // 图片加载完成后淡入
            img.onload = function() {
                imageContainer.style.opacity = '1';
                imageContainer.style.transform = 'scale(1)';
            };

            // 更新计数器
            updateImageCounter();
        }
    }, 150);
}

function updateImageCounter() {
    const imageCounter = document.getElementById('image-counter');
    if (!imageCounter) return;

    const total = currentImageViewerState.images.length;
    const current = currentImageViewerState.currentIndex + 1;
    imageCounter.textContent = `${current}/${total}`;
}

function updateImageNavigation() {
    const prevImage = document.getElementById('prev-image');
    const nextImage = document.getElementById('next-image');
    const imageCounter = document.getElementById('image-counter');

    if (!prevImage || !nextImage || !imageCounter) return;

    if (currentImageViewerState.images.length <= 1) {
        prevImage.style.display = 'none';
        nextImage.style.display = 'none';
        imageCounter.style.display = 'none';
    } else {
        prevImage.style.display = 'flex';
        nextImage.style.display = 'flex';
        imageCounter.style.display = 'block';
    }
}

// 视频查看器相关函数
function closeVideoViewerHandler() {
    const videoViewerModal = document.getElementById('video-viewer-modal');
    const videoPlayer = document.getElementById('video-player');

    if (!videoViewerModal || !videoPlayer) return;

    // 暂停视频
    videoPlayer.pause();

    // 隐藏弹窗
    videoViewerModal.style.display = 'none';
}

// 格式化消息时间
function formatMessageTime(date) {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
}

// 显示登录页面
function showLoginModal() {
    // 检查是否已经存在登录页面容器，如果存在则不重复创建
    const existingContainer = document.getElementById('login-page-container');
    if (existingContainer) {
        return;
    }

    fetch('partials/login-page.html?v=' + Date.now(), { cache: 'no-store' })
        .then(res => res.text())
        .then(html => {
            const loginPageDiv = document.createElement('div');
            loginPageDiv.innerHTML = html;
            loginPageDiv.id = 'login-page-container';
            document.body.appendChild(loginPageDiv);

            // 确保DOM元素已经插入后再绑定事件
            setTimeout(() => {
                bindLoginPageEvents();
            }, 10);
        });
}

// 防止重复绑定事件
let loginEventsBindingFlag = false;

function bindLoginPageEvents() {
    // 如果已经绑定过，就不再重复绑定
    if (loginEventsBindingFlag) {
        return;
    }

    // 使用事件委托，直接在document上监听点击事件
    document.addEventListener('click', loginPageClickHandler);

    // 支持回车键
    document.addEventListener('keypress', loginPageKeyHandler);

    // 标记已绑定
    loginEventsBindingFlag = true;
}

function loginPageClickHandler(e) {
    // 只处理登录页面相关的元素
    if (!document.getElementById('login-page-container')) {
        return;
    }

    // 登录按钮
    if (e.target && e.target.id === 'login-btn') {
        e.preventDefault();
        handleLogin();
        return;
    }

    // 注册按钮
    if (e.target && e.target.id === 'register-btn') {
        e.preventDefault();
        handleRegister();
        return;
    }

    // 切换到注册
    if (e.target && e.target.id === 'show-register') {
        e.preventDefault();
        showRegisterForm();
        return;
    }

    // 切换到登录
    if (e.target && e.target.id === 'show-login') {
        e.preventDefault();
        showLoginForm();
        return;
    }
}

function loginPageKeyHandler(e) {
    // 只处理登录页面相关的元素
    if (!document.getElementById('login-page-container')) {
        return;
    }

    if (e.key === 'Enter') {
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.id === 'login-username' || activeElement.id === 'login-password')) {
            const loginBtn = document.getElementById('login-btn');
            if (loginBtn) loginBtn.click();
        } else if (activeElement && (activeElement.id === 'register-username' || activeElement.id === 'register-password' || activeElement.id === 'register-avatar')) {
            const registerBtn = document.getElementById('register-btn');
            if (registerBtn) registerBtn.click();
        }
    }
}

function showRegisterForm() {
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    if (loginForm) {
        loginForm.style.display = 'none';
    }
    if (registerForm) {
        registerForm.style.display = 'block';
    }

    // 隐藏错误提示
    hideLoginAlert();
}

function showLoginForm() {
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    if (loginForm) {
        loginForm.style.display = 'block';
    }
    if (registerForm) {
        registerForm.style.display = 'none';
    }

    // 隐藏错误提示
    hideLoginAlert();
}

function handleLogin() {
    const usernameInput = document.getElementById('login-username');
    const passwordInput = document.getElementById('login-password');
    const loginBtn = document.getElementById('login-btn');

    if (!usernameInput || !passwordInput || !loginBtn) {
        return;
    }

    const username = usernameInput.value.trim();
    const password = passwordInput.value;

    if (!username || !password) {
        showLoginAlert('请输入用户名和密码', 'error');
        return;
    }

    // 显示加载状态
    loginBtn.disabled = true;
    loginBtn.textContent = '登录中...';

    fetch('/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ username, password })
    })
    .then(res => {
        return res.json();
    })
    .then(data => {
        if (data.code === 0) {
            localStorage.setItem('wechat_user', JSON.stringify(data.user));

            // 移除登录页面和清理事件监听器
            const loginPageContainer = document.getElementById('login-page-container');
            if (loginPageContainer) {
                loginPageContainer.remove();
                // 清理事件监听器
                document.removeEventListener('click', loginPageClickHandler);
                document.removeEventListener('keypress', loginPageKeyHandler);
                loginEventsBindingFlag = false;
            }

            updateCurrentUser();

            // 初始化WebSocket连接
            if (window.wsManager) {
                window.wsManager.init();
            }

            // 重置通讯录初始化状态，确保登录后能重新获取数据
            if (typeof window.resetContactsInitialization === 'function') {
                window.resetContactsInitialization();
            } else if (typeof window.contactsInitialized !== 'undefined') {
                window.contactsInitialized = false;
            }

            // 登录成功后跳转到聊天页面
            showPage('chat');

            // 如果当前页面是moments-page，调用fetchMoments刷新朋友圈
            const activePage = document.querySelector('.page.active');
            if (activePage && activePage.id === 'moments-page' && typeof fetchMoments === 'function') {
                fetchMoments();
            } else {
                // 刷新聊天列表和其他数据
                if (typeof fetchChats === 'function') {
                    fetchChats();
                }

                // 如果不在聊天页面，切换到聊天页面
                const chatPage = document.getElementById('chat-page');
                const pages = document.querySelectorAll('.page');
                if (chatPage && pages) {
                    pages.forEach(page => {
                        page.classList.remove('active');
                    });
                    chatPage.classList.add('active');

                    // 切换底部导航
                    const tabItems = document.querySelectorAll('.tab-item');
                    tabItems.forEach(tab => {
                        tab.classList.remove('active');
                        if (tab.getAttribute('data-page') === 'chat-page') {
                            tab.classList.add('active');
                        }
                    });
                }

                // 重新初始化聊天页面
                if (typeof initChat === 'function') {
                    setTimeout(() => {
                        initChat();
                    }, 100);
                }
            }
        } else {
            showLoginAlert(data.msg || '登录失败', 'error');
        }
    })
    .catch(error => {
        console.error('登录错误', error);
        showLoginAlert('网络错误', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        loginBtn.disabled = false;
        loginBtn.textContent = '登录';
    });
}

function handleRegister() {
    const usernameInput = document.getElementById('register-username');
    const passwordInput = document.getElementById('register-password');
    const registerBtn = document.getElementById('register-btn');

    if (!usernameInput || !passwordInput || !registerBtn) {
        return;
    }

    const username = usernameInput.value.trim();
    const password = passwordInput.value;
    const avatar = '';

    if (!username || !password) {
        showLoginAlert('请输入用户名和密码', 'error');
        return;
    }

    // 显示加载状态
    registerBtn.disabled = true;
    registerBtn.textContent = '注册中...';

    fetch('/api/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password, avatar })
    })
    .then(res => {
        return res.json();
    })
    .then(data => {
        if (data.code === 0) {
            showLoginAlert('注册成功，请登录', 'success');
            setTimeout(() => {
                showLoginForm();
            }, 2000);
        } else {
            showLoginAlert(data.msg || '注册失败', 'error');
        }
    })
    .catch(error => {
        console.error('注册错误', error);
        showLoginAlert('网络错误', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        registerBtn.disabled = false;
        registerBtn.textContent = '注册';
    });
}

// 显示登录错误提示
function showLoginAlert(message, type = 'error') {
    const alertElement = document.getElementById('login-alert');
    const alertText = document.getElementById('alert-text');
    const alertIcon = alertElement?.querySelector('.alert-icon i');

    if (!alertElement || !alertText) return;

    // 设置消息内容
    alertText.textContent = message;

    // 设置图标和样式
    if (type === 'success') {
        alertElement.classList.add('success');
        alertElement.classList.remove('error');
        if (alertIcon) {
            alertIcon.className = 'fas fa-check-circle';
        }
    } else {
        alertElement.classList.add('error');
        alertElement.classList.remove('success');
        if (alertIcon) {
            alertIcon.className = 'fas fa-exclamation-triangle';
        }
    }

    // 显示提示
    alertElement.classList.add('show');

    // 自动隐藏（成功提示2秒，错误提示5秒）
    const autoHideDelay = type === 'success' ? 2000 : 5000;
    setTimeout(() => {
        hideLoginAlert();
    }, autoHideDelay);
}

// 隐藏登录错误提示
function hideLoginAlert() {
    const alertElement = document.getElementById('login-alert');
    if (alertElement) {
        alertElement.classList.remove('show');
    }
}

// 全局函数，供HTML中的onclick使用
window.hideLoginAlert = hideLoginAlert;

// 移除用户信息栏（已在"我"页面显示，无需重复）
function showUserInfo() {
    // 移除现有的用户信息栏
    const userDiv = document.getElementById('user-info-bar');
    if (userDiv) {
        userDiv.remove();
    }
}

// 现代化Toast提示系统
function showToast(message, type = 'info', duration = 3000, title = null) {
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        console.error('Toast容器未找到');
        return;
    }

    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    // 构建内容
    let contentHTML = '';
    if (title) {
        contentHTML = `
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
        `;
    } else {
        contentHTML = `
            <div class="toast-content">${message}</div>
        `;
    }

    toast.innerHTML = `
        <div class="toast-icon">
            <i class="fas ${getToastIcon(type)}"></i>
        </div>
        ${contentHTML}
        <button class="toast-close" onclick="hideToast(this)">
            <i class="fas fa-times"></i>
        </button>
        <div class="toast-progress"></div>
    `;

    // 添加到容器
    toastContainer.appendChild(toast);

    // 添加点击关闭功能
    toast.addEventListener('click', () => {
        hideToast(toast);
    });

    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // 自动隐藏（错误类型延长显示时间）
    const autoDuration = type === 'error' ? 6000 : duration; // 错误提示显示6秒
    setTimeout(() => {
        hideToast(toast);
    }, autoDuration);

    return toast;
}

function hideToast(element) {
    const toast = element.closest ? element.closest('.toast') : element;
    if (toast) {
        toast.classList.add('hide');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 400);
    }
}

function getToastIcon(type) {
    switch (type) {
        case 'success': return 'fa-check';
        case 'error': return 'fa-exclamation-triangle';
        case 'warning': return 'fa-exclamation';
        case 'info': return 'fa-info-circle';
        default: return 'fa-info-circle';
    }
}

// 快捷方法
function showSuccessToast(message, title = null) {
    return showToast(message, 'success', 3000, title);
}

function showErrorToast(message, title = null) {
    return showToast(message, 'error', 6000, title); // 错误提示6秒后自动消失
}

function showWarningToast(message, title = null) {
    return showToast(message, 'warning', 4000, title);
}

function showInfoToast(message, title = null) {
    return showToast(message, 'info', 3000, title);
}

// 显示加载状态
function showLoading(element, text = '加载中...') {
    if (!element) return;

    const originalContent = element.innerHTML;
    element.dataset.originalContent = originalContent;
    element.innerHTML = `<span class="loading-spinner"></span> ${text}`;
    element.disabled = true;
}

// 隐藏加载状态
function hideLoading(element) {
    if (!element) return;

    const originalContent = element.dataset.originalContent;
    if (originalContent) {
        element.innerHTML = originalContent;
        delete element.dataset.originalContent;
    }
    element.disabled = false;
}

// 创建骨架屏
function createSkeleton(type = 'text', count = 1) {
    const container = document.createElement('div');

    for (let i = 0; i < count; i++) {
        const skeleton = document.createElement('div');
        skeleton.className = 'skeleton';

        switch (type) {
            case 'text':
                skeleton.classList.add('skeleton-text');
                if (Math.random() > 0.5) {
                    skeleton.classList.add('short');
                } else {
                    skeleton.classList.add('long');
                }
                break;
            case 'avatar':
                skeleton.classList.add('skeleton-avatar');
                break;
            case 'image':
                skeleton.classList.add('skeleton-image');
                break;
            case 'chat-item':
                skeleton.innerHTML = `
                    <div style="display: flex; align-items: center; padding: 16px;">
                        <div class="skeleton skeleton-avatar" style="margin-right: 16px;"></div>
                        <div style="flex: 1;">
                            <div class="skeleton skeleton-text short" style="margin-bottom: 8px;"></div>
                            <div class="skeleton skeleton-text long"></div>
                        </div>
                    </div>
                `;
                break;
        }

        container.appendChild(skeleton);
    }

    return container;
}

// 添加在线状态指示器
function addOnlineIndicator(avatarElement, isOnline = true) {
    if (!avatarElement) return;

    // 移除现有的指示器
    const existingIndicator = avatarElement.querySelector('.online-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // 添加新的指示器
    const indicator = document.createElement('div');
    indicator.className = `online-indicator ${isOnline ? '' : 'offline'}`;
    avatarElement.style.position = 'relative';
    avatarElement.appendChild(indicator);
}

// 添加通知徽章
function addNotificationBadge(element, count) {
    if (!element) return;

    // 移除现有的徽章
    const existingBadge = element.querySelector('.notification-badge');
    if (existingBadge) {
        existingBadge.remove();
    }

    if (count > 0) {
        const badge = document.createElement('div');
        badge.className = 'notification-badge';
        badge.textContent = count > 99 ? '99+' : count.toString();
        element.style.position = 'relative';
        element.appendChild(badge);
    }
}

// 平滑滚动到元素
function smoothScrollTo(element, offset = 0) {
    if (!element) return;

    const elementPosition = element.offsetTop - offset;
    const startPosition = window.pageYOffset;
    const distance = elementPosition - startPosition;
    const duration = 500;
    let start = null;

    function animation(currentTime) {
        if (start === null) start = currentTime;
        const timeElapsed = currentTime - start;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
    }

    function ease(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }

    requestAnimationFrame(animation);
}

// 防抖函数
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 检测设备类型
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 复制到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showToast('已复制到剪贴板', 'success');
        return true;
    } catch (err) {
        console.error('复制失败:', err);
        showToast('复制失败', 'error');
        return false;
    }
}

// 图片压缩
function compressImage(file, maxWidth = 800, quality = 0.8) {
    return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = function() {
            const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
            canvas.width = img.width * ratio;
            canvas.height = img.height * ratio;

            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            canvas.toBlob(resolve, 'image/jpeg', quality);
        };

        img.src = URL.createObjectURL(file);
    });
}

// 添加页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面加载动画
    document.body.classList.add('fade-in-up');

    // 检测设备类型并添加相应的类
    if (isMobile()) {
        document.body.classList.add('mobile-device');
    } else {
        document.body.classList.add('desktop-device');
    }

    // 添加触摸设备支持
    if ('ontouchstart' in window) {
        document.body.classList.add('touch-device');
    }
});

// 快捷键提示功能
function showKeyboardShortcutsHint() {
    // 检查是否已经显示过提示
    if (localStorage.getItem('shortcuts_hint_shown')) {
        return;
    }

    const hintHtml = `
        <div class="keyboard-shortcuts-hint" id="shortcuts-hint">
            <div class="shortcut"><kbd>Ctrl</kbd> + <kbd>F</kbd> 搜索消息</div>
            <div class="shortcut"><kbd>Ctrl</kbd> + <kbd>E</kbd> 导出聊天记录</div>
            <div class="shortcut"><kbd>Ctrl</kbd> + <kbd>I</kbd> 查看聊天统计</div>
            <div class="shortcut"><kbd>Esc</kbd> 关闭对话框</div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', hintHtml);

    const hint = document.getElementById('shortcuts-hint');

    // 显示提示
    setTimeout(() => {
        hint.classList.add('show');
    }, 2000);

    // 5秒后自动隐藏
    setTimeout(() => {
        hint.classList.remove('show');
        setTimeout(() => {
            hint.remove();
        }, 300);
    }, 7000);

    // 标记已显示过
    localStorage.setItem('shortcuts_hint_shown', 'true');
}

// 页面加载完成后显示快捷键提示
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(showKeyboardShortcutsHint, 3000);

    // 初始化全局WebSocket监听器（用于接收消息和更新徽标）
    setTimeout(() => {
        if (typeof initGlobalWebSocketListeners === 'function') {
            initGlobalWebSocketListeners();
        } else {
            console.warn('initGlobalWebSocketListeners函数未找到，将在2秒后重试');
            setTimeout(() => {
                if (typeof initGlobalWebSocketListeners === 'function') {
                    initGlobalWebSocketListeners();
                }
            }, 2000);
        }
    }, 1000);

    // 预初始化聊天模块的基本功能（确保跨页面调用可用）
    setTimeout(() => {
        if (typeof initChat === 'function') {

            initChat();
        }
    }, 1500);
});