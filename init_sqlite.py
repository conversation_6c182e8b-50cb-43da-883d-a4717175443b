#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite数据库初始化脚本
"""

import sqlite3
import os
from datetime import datetime

def init_database():
    """初始化SQLite数据库"""
    db_path = 'wechat.db'
    
    # 如果数据库文件存在，先删除
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"已删除现有数据库文件: {db_path}")
    
    # 创建新的数据库连接
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 用户表
        cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            wechat_id VARCHAR(6) UNIQUE,
            password VARCHAR(255) NOT NULL,
            avatar VARCHAR(255),
            cover_type TEXT DEFAULT 'image' CHECK(cover_type IN ('image', 'video')),
            cover_url VARCHAR(255),
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            last_seen DATETIME,
            is_online INTEGER DEFAULT 0,
            online_status INTEGER DEFAULT 0
        )
        ''')
        
        # 2. 好友关系表
        cursor.execute('''
        CREATE TABLE friends (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            friend_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(user_id, friend_id),
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            FOREIGN KEY (friend_id) REFERENCES users (id) ON DELETE CASCADE
        )
        ''')
        
        # 3. 私聊消息表
        cursor.execute('''
        CREATE TABLE messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sender_id INTEGER NOT NULL,
            receiver_id INTEGER,
            content TEXT NOT NULL,
            message_type VARCHAR(20) DEFAULT 'text',
            file_url VARCHAR(255),
            file_name VARCHAR(255),
            file_size INTEGER,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            read_status INTEGER DEFAULT 0,
            FOREIGN KEY (sender_id) REFERENCES users (id),
            FOREIGN KEY (receiver_id) REFERENCES users (id)
        )
        ''')
        
        # 4. 群组表
        cursor.execute('''
        CREATE TABLE groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) NOT NULL,
            avatar VARCHAR(255),
            owner_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (owner_id) REFERENCES users (id)
        )
        ''')
        
        # 5. 群成员表
        cursor.execute('''
        CREATE TABLE group_members (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            group_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            role TEXT DEFAULT 'member' CHECK(role IN ('owner', 'admin', 'member')),
            UNIQUE(group_id, user_id),
            FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
        ''')
        
        # 6. 群聊消息表
        cursor.execute('''
        CREATE TABLE group_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            group_id INTEGER NOT NULL,
            sender_id INTEGER NOT NULL,
            content TEXT,
            message_type VARCHAR(20) DEFAULT 'text',
            file_url VARCHAR(255),
            file_name VARCHAR(255),
            file_size INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE CASCADE,
            FOREIGN KEY (sender_id) REFERENCES users (id) ON DELETE CASCADE
        )
        ''')
        
        # 7. 群消息已读状态表
        cursor.execute('''
        CREATE TABLE group_message_reads (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            message_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(message_id, user_id),
            FOREIGN KEY (message_id) REFERENCES group_messages (id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
        ''')
        
        # 8. 朋友圈动态表
        cursor.execute('''
        CREATE TABLE moments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            content TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        # 9. 朋友圈媒体表
        cursor.execute('''
        CREATE TABLE moment_media (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            moment_id INTEGER NOT NULL,
            media_type TEXT NOT NULL CHECK(media_type IN ('image', 'video')),
            media_url VARCHAR(255) NOT NULL,
            thumbnail_url VARCHAR(255),
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (moment_id) REFERENCES moments (id)
        )
        ''')
        
        # 10. 朋友圈点赞表
        cursor.execute('''
        CREATE TABLE moment_likes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            moment_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (moment_id) REFERENCES moments (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        # 11. 朋友圈评论表
        cursor.execute('''
        CREATE TABLE moment_comments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            moment_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            content TEXT NOT NULL,
            reply_to INTEGER,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (moment_id) REFERENCES moments (id),
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (reply_to) REFERENCES moment_comments (id)
        )
        ''')
        
        # 12. 相册表
        cursor.execute('''
        CREATE TABLE albums (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            cover_image VARCHAR(255),
            cover_position TEXT DEFAULT 'top' CHECK(cover_position IN ('top', 'center', 'bottom')),
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
        ''')
        
        # 13. 相册媒体表
        cursor.execute('''
        CREATE TABLE album_media (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            album_id INTEGER NOT NULL,
            media_type TEXT NOT NULL CHECK(media_type IN ('image', 'video')),
            media_url VARCHAR(255) NOT NULL,
            thumbnail_url VARCHAR(255),
            file_name VARCHAR(255),
            file_size INTEGER,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (album_id) REFERENCES albums (id) ON DELETE CASCADE
        )
        ''')
        
        # 14. 聊天设置表
        cursor.execute('''
        CREATE TABLE chat_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            chat_id INTEGER NOT NULL,
            muted INTEGER DEFAULT 0,
            pinned INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(user_id, chat_id)
        )
        ''')
        
        # 15. 聊天删除记录表
        cursor.execute('''
        CREATE TABLE chat_deletions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            chat_with_id INTEGER NOT NULL,
            deleted_at DATETIME NOT NULL,
            UNIQUE(user_id, chat_with_id),
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (chat_with_id) REFERENCES users (id)
        )
        ''')
        
        print("数据库表创建成功！")
        
        # 插入测试数据
        insert_test_data(cursor)
        
        # 提交事务
        conn.commit()
        print("数据库初始化完成！")
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        conn.rollback()
    finally:
        conn.close()

def insert_test_data(cursor):
    """插入测试数据"""
    print("正在插入测试数据...")
    
    # 插入测试用户
    users = [
        ('king', 'king01', 'password123'),
        ('wxy', 'wxy001', 'password123'),
        ('testuser', 'test01', 'password123'),
        ('test', 'test02', 'password123'),
    ]
    
    for username, wechat_id, password in users:
        cursor.execute('''
        INSERT INTO users (username, wechat_id, password, created_at)
        VALUES (?, ?, ?, ?)
        ''', (username, wechat_id, password, datetime.now()))
    
    # 建立好友关系
    friendships = [
        (1, 2), (1, 3), (1, 4),  # king与其他人是好友
        (2, 3), (2, 4),          # wxy与testuser、test是好友
        (3, 4)                   # testuser与test是好友
    ]
    
    for user_id, friend_id in friendships:
        # 双向好友关系
        cursor.execute('INSERT INTO friends (user_id, friend_id) VALUES (?, ?)', (user_id, friend_id))
        cursor.execute('INSERT INTO friends (user_id, friend_id) VALUES (?, ?)', (friend_id, user_id))
    
    # 创建测试群组
    cursor.execute('''
    INSERT INTO groups (name, owner_id, created_at)
    VALUES ('测试群聊', 1, ?)
    ''', (datetime.now(),))
    
    group_id = cursor.lastrowid
    
    # 添加群成员
    for user_id in [1, 2, 3, 4]:
        role = 'owner' if user_id == 1 else 'member'
        cursor.execute('''
        INSERT INTO group_members (group_id, user_id, role)
        VALUES (?, ?, ?)
        ''', (group_id, user_id, role))
    
    # 创建测试相册
    albums_data = [
        (2, '我的旅行相册', '记录美好的旅行时光'),
        (2, '生活随拍', '日常生活的点点滴滴'),
        (3, '工作记录', '工作中的重要时刻'),
        (4, '美食分享', '各种美味的食物'),
    ]
    
    for user_id, name, description in albums_data:
        cursor.execute('''
        INSERT INTO albums (user_id, name, description, created_at)
        VALUES (?, ?, ?, ?)
        ''', (user_id, name, description, datetime.now()))
    
    print("测试数据插入完成！")

if __name__ == '__main__':
    init_database()
